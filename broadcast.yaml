openapi: 3.0.3
info:
  title: Broadcast API
  description: API for creating and checking the status of broadcast messages.
  version: 1.0.0

paths:
  /v1/broadcast/create:
    post:
      summary: Create a new broadcast message
      description: Requires an admin session (JWT Bearer token). Sends a broadcast to all users.
      tags:
        - Broadcast
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
                - message
              properties:
                title:
                  type: string
                  example: "System Maintenance"
                message:
                  type: string
                  example: "We will be undergoing maintenance at 2 AM UTC."
                image_content:
                  type: string
                  nullable: true
                  example: "base64encodedimagestring..."
      responses:
        '200':
          description: Broadcast created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      message:
                        type: string
        '400':
          description: Client-side validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v1/broadcast/status:
    get:
      summary: Get status of a broadcast
      description: Requires an admin session (<PERSON><PERSON><PERSON> Bearer token).
      tags:
        - Broadcast
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: id
          schema:
            type: string
          required: true
          description: Broadcast ID
      responses:
        '200':
          description: Broadcast status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      status:
                        type: string
                        example: "in_progress"
                      progress:
                        type: number
                        format: float
                        example: 74.5
                      total_users:
                        type: integer
                        example: 1000
                      sent_count:
                        type: integer
                        example: 745
                      failed_count:
                        type: integer
                        example: 10
        '400':
          description: Missing session or broadcast ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: object
          properties:
            id:
              type: integer
              example: 1
            message:
              type: string
              example: "Invalid session cookie. Please log in again."
